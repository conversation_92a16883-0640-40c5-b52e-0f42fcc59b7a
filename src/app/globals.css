@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 11 16 19; /* #0b1013 */
    --foreground: 250 250 250; /* #fafafa */
    --card: 11 16 19;
    --card-foreground: 250 250 250;
    --popover: 11 16 19;
    --popover-foreground: 250 250 250;
    --primary: 204 175 133; /* #ccaf85 - orange accent */
    --primary-foreground: 11 16 19;
    --secondary: 53 61 81; /* #353d51 */
    --secondary-foreground: 250 250 250;
    --muted: 52 48 46; /* #34302e */
    --muted-foreground: 161 161 170;
    --accent: 78 154 203; /* #4e9acb - blue accent */
    --accent-foreground: 11 16 19;
    --destructive: 193 43 51; /* #c12b33 */
    --destructive-foreground: 250 250 250;
    --border: 39 39 42;
    --input: 39 39 42;
    --ring: 204 175 133;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
    background: linear-gradient(180deg, #0b1013 0%, #000 100%);
    min-height: 100vh;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary via-yellow-400 to-accent bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradient-shift 4s ease-in-out infinite;
    font-weight: 700;
    text-shadow: 0 0 30px rgba(139, 92, 246, 0.3);
    filter: brightness(1.2) contrast(1.1);
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 200% 50%; }
    75% { background-position: 100% 50%; }
  }

  .hero-gradient {
    background: radial-gradient(ellipse at center, rgba(204, 175, 133, 0.1) 0%, rgba(11, 16, 19, 0.8) 70%);
  }

  /* Hide scrollbars */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 推特嵌入样式 - 最小干扰 */
  .twitter-content {
    width: 100%;
    max-width: 100%;
    min-height: 300px;
  }

  /* 推特嵌入完成后的样式 */
  .twitter-content .twitter-tweet-rendered {
    margin: 0 auto !important;
    max-width: 100% !important;
  }

  /* 推特加载前的blockquote样式 */
  .twitter-content blockquote.twitter-tweet:not(.twitter-tweet-rendered) {
    margin: 0;
    padding: 16px;
    border-left: 4px solid #1da1f2;
    background: rgba(29, 161, 242, 0.1);
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  }

  .twitter-content blockquote.twitter-tweet:not(.twitter-tweet-rendered) p {
    margin: 0 0 8px 0;
    color: #ffffff;
    line-height: 1.4;
    font-size: 14px;
  }

  .twitter-content blockquote.twitter-tweet:not(.twitter-tweet-rendered) a {
    color: #1da1f2;
    text-decoration: none;
  }

  .twitter-content blockquote.twitter-tweet:not(.twitter-tweet-rendered) a:hover {
    text-decoration: underline;
  }

  /* 确保推特iframe正常显示 */
  .twitter-content iframe {
    max-width: 100% !important;
    border-radius: 8px;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .hero-gradient {
      background: radial-gradient(ellipse at center, rgba(204, 175, 133, 0.05) 0%, rgba(11, 16, 19, 0.9) 50%);
    }
  }

  /* 优化滚动性能 - 移除smooth scrolling减少卡顿 */
  html {
    scroll-behavior: auto;
  }

  /* 性能优化 - 启用硬件加速 */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeSpeed;
  }

  /* 优化滚动性能 */
  .scrollable-container {
    -webkit-overflow-scrolling: touch;
    will-change: scroll-position;
  }

  /* 减少重绘和回流 */
  .video-container, .twitter-embed-container {
    contain: layout style paint;
    will-change: transform;
  }

  /* 全局性能优化 */
  * {
    box-sizing: border-box;
  }

  /* 禁用不必要的动画 */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* 优化图片和视频加载 */
  img, video {
    content-visibility: auto;
    contain-intrinsic-size: 1px 1000px;
  }

  /* 轻量化视频悬停效果 */
  .video-hover-effect {
    transition: transform 0.2s ease, opacity 0.2s ease;
    will-change: transform;
  }

  .video-hover-effect:hover {
    transform: scale(1.01);
  }

  /* 文本截断样式 */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* 模态框动画 */
  .modal-enter {
    animation: modalFadeIn 0.3s ease-out;
  }

  .modal-exit {
    animation: modalFadeOut 0.3s ease-in;
  }

  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes modalFadeOut {
    from {
      opacity: 1;
      transform: scale(1);
    }
    to {
      opacity: 0;
      transform: scale(0.9);
    }
  }

  /* 轻量化视频卡片悬停效果 */
  .video-card {
    transition: transform 0.15s ease;
    will-change: transform;
  }

  .video-card:hover {
    transform: translateY(-2px);
  }

  /* 播放按钮脉冲动画 */
  .play-button-pulse {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.8;
    }
  }

  /* 响应式视频容器 */
  .responsive-video {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
  }

  .responsive-video video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* 按钮点击颤动动画效果 */
  @keyframes button-click {
    0% { transform: scale(1); }
    50% { transform: scale(0.98); }
    100% { transform: scale(1); }
  }

  .btn-feedback {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .btn-feedback:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
  }

  .btn-feedback:active {
    animation: button-click 0.2s ease-out;
  }

  /* 增强主要按钮的效果 */
  .btn-feedback.btn-primary {
    background: linear-gradient(135deg, 
      rgba(204, 175, 133, 0.9) 0%, 
      rgba(204, 175, 133, 0.7) 50%, 
      rgba(204, 175, 133, 0.9) 100%);
    box-shadow: 
      0 8px 32px rgba(204, 175, 133, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .btn-feedback.btn-primary:hover {
    background: linear-gradient(135deg, 
      rgba(204, 175, 133, 1) 0%, 
      rgba(204, 175, 133, 0.8) 50%, 
      rgba(204, 175, 133, 1) 100%);
    box-shadow: 
      0 12px 40px rgba(204, 175, 133, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  /* 按钮加载状态 */
  .btn-loading {
    position: relative;
    pointer-events: none;
  }

  .btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

@layer utilities {
  /* 自定义工具类 */
  .backdrop-blur-strong {
    backdrop-filter: blur(20px);
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .border-gradient {
    border: 1px solid;
    border-image: linear-gradient(45deg, rgba(204, 175, 133, 0.3), rgba(78, 154, 203, 0.3)) 1;
  }
}
/* Force CSS refresh - 05/28/2025 18:43:11 */

import type { Metadata } from 'next'
import { HomeContentSimple } from '@/components/HomeContentSimple'

export const metadata: Metadata = {
  title: 'Flux Kontext AI - Профессиональная Платформа Генерации Изображений ИИ | Создавайте Потрясающие Изображения',
  description: 'Превратите свои идеи в профессиональные изображения с помощью нашей передовой технологии ИИ. Генерируйте изображения из текста, редактируйте существующие изображения и обрабатывайте множество изображений с мощью Flux Kontext AI.',
  openGraph: {
    title: 'Flux Kontext AI - Профессиональная Платформа Генерации Изображений ИИ',
    url: 'https://fluxkontext.space/ru',
    locale: 'ru_RU',
    type: 'website',
  }
}

const ruDictionary = {
  hero: {
    badge: "Профессиональная Платформа Генерации Изображений ИИ",
    title: "Создавайте Потрясающие Изображения с",
    subtitle: "Flux Kontext AI",
    description: "Превратите свои идеи в профессиональные изображения с помощью нашей передовой технологии ИИ. Генерируйте изображения из текста, редактируйте существующие изображения и обрабатывайте множество изображений с мощью Flux Kontext AI.",
    cta: {
      primary: "Начать Создавать",
      secondary: "Посмотреть Цены"
    }
  },
  features: {
    title: "Ключевые Особенности Платформы Flux Kontext AI",
    subtitle: "Наш Flux Kontext AI объединяет передовые технологии для предоставления профессиональной генерации и редактирования изображений на одной бесшовной платформе.",
    items: [
      {
        title: "Генерация Текст-Изображение",
        description: "Превращайте ваши текстовые описания в потрясающие, высококачественные изображения с помощью передовой технологии ИИ."
      },
      {
        title: "Профессиональное Редактирование Изображений",
        description: "Редактируйте существующие изображения с помощью инструкций на естественном языке для точных модификаций."
      },
      {
        title: "Обработка Множественных Изображений",
        description: "Обрабатывайте множество изображений одновременно с последовательным стилем и качеством."
      }
    ]
  },
  faq: {
    title: "Часто Задаваемые Вопросы",
    subtitle: "Найдите ответы на распространенные вопросы о нашей платформе Flux Kontext AI и её мощных функциях генерации изображений.",
    items: [
      {
        question: "Что такое Flux Kontext AI?",
        answer: "Flux Kontext AI - это передовая платформа генерации изображений, которая использует современный искусственный интеллект для создания потрясающих изображений из текстовых описаний, редактирования существующих изображений и обработки множества изображений одновременно."
      }
    ]
  },
  cta: {
    title: "Готовы Создавать Удивительные Изображения?",
    subtitle: "Присоединяйтесь к тысячам создателей, которые используют Flux Kontext AI для воплощения своих идей в жизнь.",
    button: "Начать Сейчас"
  },
  footer: {
    brand: {
      name: "Flux Kontext",
      description: "Профессиональная платформа генерации изображений ИИ.",
      copyright: "© 2025 Flux Kontext. Все права защищены."
    },
    contact: {
      title: "Контакты",
      email: "<EMAIL>"
    },
    legal: {
      title: "Правовая информация",
      terms: "Условия Обслуживания",
      privacy: "Политика Конфиденциальности",
      refund: "Политика Возврата"
    },
    languages: {
      title: "Языки"
    },
    social: {
      builtWith: "Создано с ❤️ для творцов по всему миру",
      followUs: "Следите за нами в"
    }
  }
}

export default function RussianPage() {
  return <HomeContentSimple dictionary={ruDictionary} />
} 
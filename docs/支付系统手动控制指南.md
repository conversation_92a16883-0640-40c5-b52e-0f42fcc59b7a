# 🔧 支付系统手动控制完整指南

## 📁 核心配置文件位置
```
src/lib/config/payment.ts  ← 这是唯一的控制中心
```

## 🎯 如何编辑配置文件

### 1️⃣ **基本开关控制**
```typescript
export const PAYMENT_CONFIG = {
  // === 🎯 主要控制开关 ===
  STRIPE_ENABLED: true,        // ✅启用 ❌关闭 Stripe支付
  CREEM_ENABLED: true,         // ✅启用 ❌关闭 Creem支付
  DEFAULT_PROVIDER: "creem",   // 默认提供商: "stripe" | "creem"
  MAINTENANCE_MODE: false,     // 🚧维护模式（暂停所有支付）
}
```

### 2️⃣ **常用配置场景**

#### 🔴 **只启用Stripe**
```typescript
STRIPE_ENABLED: true,
CREEM_ENABLED: false,
DEFAULT_PROVIDER: "stripe",
```

#### 🟢 **只启用Creem**
```typescript
STRIPE_ENABLED: false,
CREEM_ENABLED: true,
DEFAULT_PROVIDER: "creem",
```

#### 🟡 **维护模式（暂停所有支付）**
```typescript
MAINTENANCE_MODE: true,  // 其他配置不变
```

#### 🔵 **强制使用指定提供商**
```typescript
FORCE_PROVIDER: "stripe",  // 强制所有用户使用Stripe
// 或
FORCE_PROVIDER: "creem",   // 强制所有用户使用Creem
// 或
FORCE_PROVIDER: null,      // 取消强制，恢复智能选择
```

### 3️⃣ **配置更新流程**
```bash
# 1. 编辑配置文件
code src/lib/config/payment.ts

# 2. 检查配置状态
node scripts/check-payment-config.js

# 3. 重启开发服务器
# Ctrl+C 停止服务器
npm run dev

# 4. 提交到Git（重要！）
git add src/lib/config/payment.ts
git commit -m "🔧 更新支付配置: [描述你的修改]"
```

### 4️⃣ **必须填写的字段**
```typescript
// === 📝 配置说明（必填）===
LAST_UPDATED: "2025-01-20",     // 📅 更新日期
UPDATED_BY: "管理员姓名",        // 👤 更新人员
NOTES: "修改原因和说明"          // 📝 修改备注
```

## 🔍 强制使用 vs 双系统的区别

### 🔒 **强制使用模式**
```typescript
FORCE_PROVIDER: "stripe"  // 所有用户只能用Stripe
```
**场景**：
- 🚨 紧急情况：某个支付系统出故障
- 🔧 维护期间：需要测试单一系统
- 📊 数据分析：统一支付渠道便于分析
- 💰 成本控制：某个系统手续费更低

### ⚖️ **双系统模式**
```typescript
FORCE_PROVIDER: null      // 智能选择或用户选择
```
**场景**：
- 🌍 用户体验：不同地区用户用不同系统
- 🔄 负载均衡：分散支付压力
- 🛡️ 风险分散：避免单点故障
- 💳 支付方式：满足不同用户偏好

## 🌍 两个支付系统的配置需求

### 💳 **Stripe配置**
```bash
# .env.local 需要的环境变量
STRIPE_PRIVATE_KEY=sk_test_...     # Stripe私钥
STRIPE_PUBLIC_KEY=pk_test_...      # Stripe公钥
STRIPE_WEBHOOK_SECRET=whsec_...    # Webhook密钥
```

**Stripe特点**：
- 🌍 全球支付，支持200+国家
- 💰 手续费：2.9% + $0.30
- 🏢 企业级功能完整
- 📊 详细的数据分析
- 🔒 高级安全特性

### 🇨🇳 **Creem配置**
```bash
# .env.local 需要的环境变量
CREEM_API_KEY=your_api_key         # Creem API密钥
CREEM_API_URL=https://api.creem.io # Creem API地址
CREEM_WEBHOOK_SECRET=your_secret   # Webhook密钥
CREEM_SUCCESS_URL=https://...      # 支付成功回调
```

**Creem特点**：
- 🇨🇳 专为中国开发者设计
- 💰 手续费更低
- 🚀 接入简单快速
- 💳 支持支付宝、微信支付
- 📱 移动端体验优秀

## 🗄️ 数据库和API说明

### 📊 **当前项目使用的技术栈**
```typescript
// 认证系统
NextAuth v4 + JWT

// 数据库
PostgreSQL + Prisma ORM

// 支付API
Stripe API + Creem API

// 部署
Vercel + Supabase
```

### 🗃️ **数据库表结构**
```sql
-- 用户表
users (id, email, name, image, ...)

-- 支付订单表（统一存储双支付系统）
payment_orders (
  id,
  userId,
  paymentProvider,    -- "stripe" 或 "creem"
  stripeSessionId,    -- Stripe专用字段
  creemCheckoutId,    -- Creem专用字段
  amount,
  status,
  ...
)

-- 支付配置表（备用）
payment_configs (
  id,
  stripeEnabled,
  creemEnabled,
  defaultProvider,
  ...
)
```

## 🔧 实际操作示例

### 📝 **示例1：紧急切换到Stripe**
```typescript
// 编辑 src/lib/config/payment.ts
export const PAYMENT_CONFIG = {
  STRIPE_ENABLED: true,
  CREEM_ENABLED: false,        // ← 关闭Creem
  FORCE_PROVIDER: "stripe",    // ← 强制使用Stripe
  MAINTENANCE_MODE: false,
  
  // 必填说明
  LAST_UPDATED: "2025-01-20",
  UPDATED_BY: "技术主管",
  NOTES: "Creem系统维护，紧急切换到Stripe"
}
```

### 📝 **示例2：恢复双系统**
```typescript
// 编辑 src/lib/config/payment.ts
export const PAYMENT_CONFIG = {
  STRIPE_ENABLED: true,
  CREEM_ENABLED: true,         // ← 重新启用Creem
  FORCE_PROVIDER: null,        // ← 取消强制，恢复智能选择
  MAINTENANCE_MODE: false,
  
  // 必填说明
  LAST_UPDATED: "2025-01-20",
  UPDATED_BY: "技术主管", 
  NOTES: "Creem维护完成，恢复双系统智能切换"
}
```

### 📝 **示例3：维护模式**
```typescript
// 编辑 src/lib/config/payment.ts
export const PAYMENT_CONFIG = {
  // 其他配置保持不变
  MAINTENANCE_MODE: true,      // ← 开启维护模式
  
  // 必填说明
  LAST_UPDATED: "2025-01-20",
  UPDATED_BY: "运维工程师",
  NOTES: "系统升级维护，暂停所有支付功能"
}
```

## ⚠️ **重要注意事项**

### 🚨 **安全提醒**
1. **配置文件包含敏感逻辑，不要分享给无关人员**
2. **每次修改都要提交Git，便于回滚**
3. **修改前先备份当前配置**
4. **测试环境先验证，再应用到生产环境**

### 🔄 **操作流程**
1. **修改配置** → 2. **检查验证** → 3. **重启服务** → 4. **提交Git**

### 📞 **紧急联系**
- 如果配置错误导致支付系统崩溃
- 立即恢复上一个Git版本
- 联系技术负责人处理

---

**📝 文档版本**: v1.0  
**📅 创建时间**: 2025-01-20  
**👨‍💻 维护者**: 技术团队  
**�� 更新频率**: 根据系统变更及时更新 
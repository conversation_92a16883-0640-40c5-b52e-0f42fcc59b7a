# 🎓 小白完整入门指南 - 从零开始理解AI视频生成项目

## 📚 目录
1. [项目概述](#项目概述)
2. [文件夹结构详解](#文件夹结构详解)
3. [核心技术栈理解](#核心技术栈理解)
4. [数据库设计详解](#数据库设计详解)
5. [双支付系统原理](#双支付系统原理)
6. [安全漏洞检查](#安全漏洞检查)
7. [API配置指南](#api配置指南)
8. [实际操作演练](#实际操作演练)

---

## 🎯 项目概述

### 这是什么项目？
这是一个**AI视频生成SaaS平台**，用户可以：
- 🎬 输入文字描述，AI生成视频
- 💳 购买积分或订阅服务
- 👤 注册登录管理账户
- 📊 查看生成历史和使用统计

### 技术架构简图
```
用户浏览器 → Next.js前端 → API接口 → 数据库
                ↓
            AI视频生成服务
                ↓
            双支付系统 (Stripe + Creem)
```

---

## 📁 文件夹结构详解

### 🏗️ 项目根目录
```
veo3.us/
├── 📁 src/                    # 源代码目录（最重要）
├── 📁 prisma/                 # 数据库配置
├── 📁 docs/                   # 文档目录
├── 📁 scripts/                # 工具脚本
├── 📁 template/               # 参考模板
├── 📄 package.json            # 项目依赖配置
├── 📄 .env.example            # 环境变量示例
└── 📄 README.md               # 项目说明
```

### 🎯 核心代码目录 (src/)
```
src/
├── 📁 app/                    # Next.js 13+ App Router
│   ├── 📁 api/               # 后端API接口
│   ├── 📁 auth/              # 登录相关页面
│   ├── 📁 dashboard/         # 用户仪表板
│   ├── 📁 pricing/           # 定价页面
│   └── 📄 page.tsx           # 首页
├── 📁 components/            # 可复用组件
├── 📁 lib/                   # 核心业务逻辑
│   ├── 📁 config/           # 配置文件
│   ├── 📁 services/         # 业务服务
│   └── 📁 utils/            # 工具函数
└── 📁 types/                # TypeScript类型定义
```

---

## 🧠 核心技术栈理解

### 前端技术
```typescript
// React + Next.js 13+ (App Router)
// 现代化的React框架，支持服务端渲染

// Tailwind CSS
// 实用优先的CSS框架，快速构建UI

// TypeScript
// JavaScript的超集，提供类型安全
```

### 后端技术
```typescript
// Next.js API Routes
// 在同一个项目中提供后端API

// NextAuth.js
// 认证解决方案，支持多种登录方式

// Prisma ORM
// 现代化的数据库工具，类型安全的数据库操作
```

### 数据库
```sql
-- PostgreSQL
-- 强大的关系型数据库

-- Supabase
-- PostgreSQL的云服务，提供实时功能
```

---

## 🗄️ 数据库设计详解

### 📊 核心数据表

#### 👤 **用户相关表**
```sql
-- 用户主表
User {
  id: 用户唯一ID
  email: 邮箱（登录用）
  name: 用户名
  credits: 积分余额
  signinCount: 登录次数
  preferredPaymentProvider: 偏好支付方式
}

-- 登录账户表（NextAuth专用）
Account {
  provider: 登录提供商（google/github）
  providerAccountId: 第三方账户ID
  access_token: 访问令牌
}

-- 会话表（NextAuth专用）
Session {
  sessionToken: 会话令牌
  expires: 过期时间
}
```

#### 💳 **支付相关表**
```sql
-- 支付订单表（核心！）
PaymentOrder {
  id: 订单ID
  userId: 用户ID
  amount: 金额（分）
  currency: 货币（USD/CNY）
  status: 状态（pending/completed/failed）
  paymentProvider: 支付提供商（stripe/creem）
  
  -- Stripe专用字段
  stripeSessionId: Stripe会话ID
  stripePaymentIntentId: Stripe支付意图ID
  
  -- Creem专用字段
  creemCheckoutId: Creem结账ID
  creemPaymentId: Creem支付ID
  
  -- 支付详情
  paidEmail: 实际支付邮箱
  paidDetail: 支付详情JSON
}

-- 订阅表
Subscription {
  userId: 用户ID
  planId: 套餐ID（basic/plus/pro）
  status: 状态（active/cancelled）
  paymentProvider: 支付提供商
  stripeSubscriptionId: Stripe订阅ID
  creemSubscriptionId: Creem订阅ID
}
```

#### 🎬 **业务相关表**
```sql
-- 视频生成记录表
VideoGeneration {
  userId: 用户ID
  prompt: 用户输入的描述
  videoUrl: 生成的视频链接
  status: 生成状态（pending/completed/failed）
  creditsUsed: 消耗的积分
}

-- 积分交易记录表
CreditTransaction {
  userId: 用户ID
  amount: 积分变化数量
  type: 交易类型（purchase/usage/refund）
  description: 交易描述
  paymentOrderId: 关联的支付订单
}
```

### 🔍 **重要！双支付系统数据库设计**

你问得很对！两个支付系统确实有不同的数据结构，但我们用了**统一数据库设计**：

```sql
-- 统一订单表设计
PaymentOrder {
  -- 通用字段
  paymentProvider: "stripe" | "creem"  -- 区分支付系统
  
  -- Stripe专用字段（只有Stripe订单才填写）
  stripeSessionId: String?
  stripePaymentIntentId: String?
  stripeCustomerId: String?
  
  -- Creem专用字段（只有Creem订单才填写）
  creemCheckoutId: String?
  creemPaymentId: String?
  creemCustomerId: String?
}
```

**为什么这样设计？**
- ✅ **查询简单**: 一条SQL获取用户所有订单
- ✅ **数据完整**: 用户订单历史不分散
- ✅ **切换无缝**: 自动切换时订单记录连续
- ✅ **统计方便**: 轻松统计总收入、订单数

---

## 🔄 双支付系统原理

### 🎯 **为什么需要双支付系统？**

#### 🌍 **地区差异**
```typescript
// 中国用户 → Creem（支持支付宝、微信）
// 国际用户 → Stripe（支持全球信用卡）
```

#### 💰 **成本优化**
```typescript
// Stripe: 2.9% + $0.30 手续费
// Creem: 更低的手续费，适合中国市场
```

#### 🛡️ **风险分散**
```typescript
// 一个系统故障时，另一个继续工作
// 避免单点故障导致收入损失
```

### 🔧 **双支付系统工作流程**

#### 1️⃣ **用户发起支付**
```typescript
// 用户点击"购买"按钮
// ↓
// 系统根据配置选择支付提供商
function determinePaymentProvider(userId, userPreference) {
  // 检查配置文件
  const config = PAYMENT_CONFIG
  
  // 维护模式？
  if (config.MAINTENANCE_MODE) return null
  
  // 强制使用某个提供商？
  if (config.FORCE_PROVIDER) return config.FORCE_PROVIDER
  
  // 用户选择？
  if (userPreference && config.ALLOW_USER_CHOICE) {
    return userPreference
  }
  
  // 默认提供商
  return config.DEFAULT_PROVIDER
}
```

#### 2️⃣ **创建支付会话**
```typescript
// Stripe流程
if (provider === "stripe") {
  const session = await stripe.checkout.sessions.create({
    line_items: [{ price_data: {...}, quantity: 1 }],
    mode: "payment",
    success_url: "https://yoursite.com/success",
    cancel_url: "https://yoursite.com/cancel"
  })
  
  // 保存到数据库
  await prisma.paymentOrder.create({
    data: {
      userId,
      paymentProvider: "stripe",
      stripeSessionId: session.id,  // Stripe专用字段
      amount,
      status: "pending"
    }
  })
}

// Creem流程
if (provider === "creem") {
  const response = await fetch(CREEM_API_URL + "/checkouts", {
    method: "POST",
    headers: { "x-api-key": CREEM_API_KEY },
    body: JSON.stringify({
      product_id: productId,
      customer: { email },
      metadata: { user_id: userId }
    })
  })
  
  // 保存到数据库
  await prisma.paymentOrder.create({
    data: {
      userId,
      paymentProvider: "creem",
      creemCheckoutId: response.id,  // Creem专用字段
      amount,
      status: "pending"
    }
  })
}
```

#### 3️⃣ **处理支付结果**
```typescript
// 两个独立的Webhook处理器

// Stripe Webhook: /api/webhooks/stripe
export async function POST(req: Request) {
  const event = stripe.webhooks.constructEvent(body, signature, secret)
  
  if (event.type === 'checkout.session.completed') {
    const session = event.data.object
    
    // 更新订单状态
    await prisma.paymentOrder.update({
      where: { stripeSessionId: session.id },
      data: { 
        status: "completed",
        paidAt: new Date(),
        paidDetail: JSON.stringify(session)
      }
    })
    
    // 添加积分
    await addUserCredits(session.metadata.userId, session.metadata.credits)
  }
}

// Creem Webhook: /api/webhooks/creem
export async function POST(req: Request) {
  const event = JSON.parse(body)
  
  if (event.eventType === 'checkout.completed') {
    const checkout = event.object
    
    // 更新订单状态
    await prisma.paymentOrder.update({
      where: { creemCheckoutId: checkout.id },
      data: { 
        status: "completed",
        paidAt: new Date(),
        paidDetail: JSON.stringify(checkout)
      }
    })
    
    // 添加积分
    await addUserCredits(checkout.metadata.user_id, checkout.metadata.credits)
  }
}
```

---

## 🔒 安全漏洞检查

### 🚨 **常见安全风险**

#### 1️⃣ **支付参数篡改**
```typescript
// ❌ 危险：直接信任前端传来的价格
const { amount, productId } = await req.json()
await createPayment({ amount, productId })  // 用户可能篡改价格！

// ✅ 安全：从服务端验证价格
const product = await getProductFromDatabase(productId)
if (product.price !== amount) {
  throw new Error("价格不匹配，可能存在篡改")
}
```

#### 2️⃣ **Webhook伪造**
```typescript
// ❌ 危险：不验证Webhook签名
export async function POST(req: Request) {
  const event = await req.json()
  // 直接处理，可能被伪造！
  await processPayment(event)
}

// ✅ 安全：验证签名
export async function POST(req: Request) {
  const signature = req.headers.get("stripe-signature")
  const event = stripe.webhooks.constructEvent(body, signature, secret)
  // 只有验证通过的事件才处理
  await processPayment(event)
}
```

#### 3️⃣ **用户权限检查**
```typescript
// ❌ 危险：不检查用户权限
export async function GET(req: Request) {
  const { userId } = await req.json()
  const orders = await getOrdersByUserId(userId)  // 任何人都能查看任何用户的订单！
  return Response.json(orders)
}

// ✅ 安全：验证用户身份
export async function GET(req: Request) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return Response.json({ error: "未登录" }, { status: 401 })
  }
  
  const orders = await getOrdersByUserId(session.user.id)  // 只能查看自己的订单
  return Response.json(orders)
}
```

### 🔍 **安全检查清单**

#### ✅ **环境变量安全**
```bash
# 检查 .env.local 文件
# 确保敏感信息不在代码中硬编码

# ✅ 正确
STRIPE_PRIVATE_KEY=sk_test_...
CREEM_API_KEY=your_secret_key

# ❌ 错误：在代码中硬编码
const STRIPE_KEY = "sk_test_hardcoded_key"  // 危险！
```

#### ✅ **API路由保护**
```typescript
// 检查所有 /api 路由是否有适当的权限验证
// 特别是支付相关的API

// 管理员API必须验证管理员权限
const adminEmails = process.env.ADMIN_EMAILS?.split(',') || []
if (!adminEmails.includes(session.user.email)) {
  return Response.json({ error: "需要管理员权限" }, { status: 403 })
}
```

#### ✅ **数据库查询安全**
```typescript
// 使用Prisma ORM自动防止SQL注入
// 但仍需注意逻辑漏洞

// ✅ 安全：使用参数化查询
const user = await prisma.user.findUnique({
  where: { id: userId }
})

// ❌ 危险：动态构建查询（虽然Prisma不允许，但要注意）
```

---

## 🔧 API配置指南

### 📝 **环境变量配置**

#### 1️⃣ **创建 .env.local 文件**
```bash
# 复制示例文件
cp .env.example .env.local

# 编辑配置
code .env.local
```

#### 2️⃣ **必需的环境变量**
```bash
# === 数据库配置 ===
DATABASE_URL="postgresql://username:password@localhost:5432/veo3_db"

# === NextAuth配置 ===
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# === Google OAuth ===
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# === GitHub OAuth ===
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# === Stripe支付 ===
STRIPE_PRIVATE_KEY="sk_test_..."
STRIPE_PUBLIC_KEY="pk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# === Creem支付 ===
CREEM_API_KEY="your-creem-api-key"
CREEM_API_URL="https://api.creem.io"
CREEM_WEBHOOK_SECRET="your-creem-webhook-secret"
CREEM_SUCCESS_URL="http://localhost:3000/payment/success"

# === AI服务 ===
SEGMIND_API_KEY="your-segmind-api-key"

# === 管理员邮箱 ===
ADMIN_EMAILS="<EMAIL>,<EMAIL>"
```

### 🔑 **获取API密钥指南**

#### 🔵 **Stripe配置**
```bash
# 1. 注册Stripe账户
# 访问: https://stripe.com

# 2. 获取API密钥
# Dashboard → Developers → API keys
# 复制 Publishable key (pk_test_...) 和 Secret key (sk_test_...)

# 3. 设置Webhook
# Dashboard → Developers → Webhooks
# 添加端点: https://yourdomain.com/api/webhooks/stripe
# 选择事件: checkout.session.completed, payment_intent.succeeded

# 4. 获取Webhook密钥
# 点击创建的Webhook → Signing secret
```

#### 🟢 **Creem配置**
```bash
# 1. 注册Creem账户
# 访问: https://creem.io

# 2. 获取API密钥
# Dashboard → Settings → API Keys
# 复制 API Key

# 3. 设置Webhook
# Dashboard → Settings → Webhooks
# 添加端点: https://yourdomain.com/api/webhooks/creem
# 选择事件: checkout.completed, subscription.active

# 4. 获取Webhook密钥
# 复制 Webhook Secret
```

#### 🔴 **Google OAuth配置**
```bash
# 1. 访问Google Cloud Console
# https://console.cloud.google.com

# 2. 创建项目或选择现有项目

# 3. 启用Google+ API
# APIs & Services → Library → Google+ API → Enable

# 4. 创建OAuth 2.0凭据
# APIs & Services → Credentials → Create Credentials → OAuth 2.0 Client IDs
# Application type: Web application
# Authorized redirect URIs: http://localhost:3000/api/auth/callback/google

# 5. 复制Client ID和Client Secret
```

#### ⚫ **GitHub OAuth配置**
```bash
# 1. 访问GitHub Settings
# https://github.com/settings/developers

# 2. 创建新的OAuth App
# New OAuth App
# Homepage URL: http://localhost:3000
# Authorization callback URL: http://localhost:3000/api/auth/callback/github

# 3. 复制Client ID和Client Secret
```

---

## 🎮 实际操作演练

### 🚀 **第一次启动项目**

#### 1️⃣ **安装依赖**
```bash
# 克隆项目
git clone <项目地址>
cd veo3.us

# 安装依赖
npm install
# 或
bun install
```

#### 2️⃣ **配置数据库**
```bash
# 生成Prisma客户端
npx prisma generate

# 推送数据库结构
npx prisma db push

# 查看数据库（可选）
npx prisma studio
```

#### 3️⃣ **启动开发服务器**
```bash
npm run dev
# 访问: http://localhost:3000
```

### 🔧 **手动控制支付系统**

#### 1️⃣ **查看当前配置**
```bash
node scripts/check-payment-config.js
```

#### 2️⃣ **修改支付配置**
```typescript
// 编辑 src/lib/config/payment.ts

// 示例：只启用Stripe
export const PAYMENT_CONFIG = {
  STRIPE_ENABLED: true,
  CREEM_ENABLED: false,        // 关闭Creem
  DEFAULT_PROVIDER: "stripe",
  MAINTENANCE_MODE: false,
  
  // 必填说明
  LAST_UPDATED: "2025-01-20",
  UPDATED_BY: "你的名字",
  NOTES: "测试期间只使用Stripe"
}
```

#### 3️⃣ **重启服务器**
```bash
# Ctrl+C 停止服务器
# 然后重新启动
npm run dev
```

#### 4️⃣ **提交更改**
```bash
git add src/lib/config/payment.ts
git commit -m "🔧 修改支付配置：只启用Stripe用于测试"
```

### 🧪 **测试支付功能**

#### 1️⃣ **Stripe测试**
```bash
# 使用Stripe测试卡号
# 卡号: 4242 4242 4242 4242
# 过期: 任何未来日期
# CVC: 任何3位数字
```

#### 2️⃣ **Creem测试**
```bash
# 使用Creem测试环境
# 按照Creem文档进行测试支付
```

### 📊 **监控和调试**

#### 1️⃣ **查看日志**
```bash
# 开发环境日志在终端显示
# 生产环境可以使用
npm run logs
```

#### 2️⃣ **数据库查询**
```bash
# 启动Prisma Studio
npx prisma studio

# 查看支付订单
# 访问: http://localhost:5555
# 选择 PaymentOrder 表
```

#### 3️⃣ **API测试**
```bash
# 使用curl测试API
curl -X GET http://localhost:3000/api/user/profile \
  -H "Authorization: Bearer <token>"
```

---

## 🎯 总结

### 🏆 **你现在应该理解的核心概念**

1. **项目结构**: Next.js + Prisma + PostgreSQL
2. **双支付系统**: Stripe + Creem 统一数据库设计
3. **手动控制**: 通过配置文件安全控制支付系统
4. **安全检查**: 参数验证、权限检查、Webhook验证
5. **API配置**: 环境变量、OAuth、支付密钥

### 📚 **下一步学习建议**

1. **熟悉代码**: 阅读 `src/app/api` 下的API代码
2. **理解数据流**: 从用户点击到支付完成的完整流程
3. **实践操作**: 修改配置、测试支付、查看数据库
4. **安全加固**: 检查所有API的权限验证
5. **监控优化**: 添加日志、错误处理、性能监控

### 🆘 **遇到问题怎么办**

1. **查看日志**: 终端输出和浏览器控制台
2. **检查配置**: 环境变量和支付配置
3. **数据库状态**: 使用Prisma Studio查看数据
4. **Git回滚**: 如果配置错误，回滚到上一个版本
5. **文档查阅**: 查看项目README和这个指南

---

**📝 文档版本**: v1.0  
**📅 创建时间**: 2025-01-20  
**👨‍💻 维护者**: 技术团队  
**🎯 适用人群**: 编程小白到中级开发者 
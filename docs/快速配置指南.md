# 🚀 快速配置指南

## 📝 第一步：创建 `.env.local` 文件

在项目根目录创建 `.env.local` 文件（注意前面有点）

### 🔥 最简配置（立即可用）

复制以下内容到 `.env.local` 文件：

```bash
# 🔍 分析工具（立即可用）
NEXT_PUBLIC_CUSTOM_ANALYTICS_DOMAIN="scripto.video"
NEXT_PUBLIC_CUSTOM_ANALYTICS_URL="https://click.pageview.click/js/script.js"

# 🔐 基础认证配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="development-secret-key-change-in-production"

# 🌐 站点配置
NEXT_PUBLIC_WEB_URL="http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME="Scripto.Video"
NEXT_PUBLIC_SITE_URL="http://localhost:3000"

# 🔧 开发环境
NODE_ENV="development"
NEXT_PUBLIC_DEFAULT_THEME="dark"
ADMIN_EMAILS="<EMAIL>"

# 📧 登录配置（开发环境）
NEXT_PUBLIC_AUTH_CREDENTIALS_ENABLED="true"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED="false"
NEXT_PUBLIC_AUTH_GITHUB_ENABLED="false"

# 💳 支付配置（开发环境禁用）
NEXT_PUBLIC_ENABLE_STRIPE="false"
NEXT_PUBLIC_ENABLE_CREEM="false"

# ☁️ 存储配置（已配置好，可直接使用）
NEXT_PUBLIC_ENABLE_R2="true"
R2_ACCOUNT_ID="e9678a567f24c2f41ae40df77744e97e"
R2_ACCESS_KEY_ID="669b0c4703363bb9dd6ca0f7ddf66816"
R2_SECRET_ACCESS_KEY="63958dfaf0875c064bd0465a77388f29affea407d457cc278bdf26f20c1166f8"
R2_BUCKET_NAME="scriptovideo"
R2_PUBLIC_URL="https://pub-12f91b2f5d5d4412a751b7664f66fbb4.r2.dev"

# 🎬 演示视频（已配置好）
NEXT_PUBLIC_DEMO_VIDEOS_URL="https://pub-49364ecf52e344d3a722a3c5bca11271.r2.dev"
```

## 🎯 第二步：如果需要数据库功能

如果你要使用用户注册、登录等功能，需要配置 Supabase：

```bash
# 🗄️ Supabase 配置（需要注册 https://supabase.com）
NEXT_PUBLIC_SUPABASE_URL="https://your-project-ref.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"
```

## 🚀 第三步：Vercel 部署配置

### 方法1：复制粘贴（推荐）

在 Vercel 项目设置 → Environment Variables 中，逐个添加以下变量：

**生产环境必需配置**：
```
NEXT_PUBLIC_CUSTOM_ANALYTICS_DOMAIN = scripto.video
NEXT_PUBLIC_CUSTOM_ANALYTICS_URL = https://click.pageview.click/js/script.js
NEXTAUTH_URL = https://scripto.video
NEXTAUTH_SECRET = your-production-secret-key-make-it-long-and-random
NEXT_PUBLIC_WEB_URL = https://scripto.video
NEXT_PUBLIC_PROJECT_NAME = Scripto.Video
NEXT_PUBLIC_SITE_URL = https://scripto.video
NODE_ENV = production
NEXT_PUBLIC_DEFAULT_THEME = dark
ADMIN_EMAILS = <EMAIL>
NEXT_PUBLIC_AUTH_CREDENTIALS_ENABLED = true
NEXT_PUBLIC_ENABLE_R2 = true
R2_ACCOUNT_ID = e9678a567f24c2f41ae40df77744e97e
R2_ACCESS_KEY_ID = 669b0c4703363bb9dd6ca0f7ddf66816
R2_SECRET_ACCESS_KEY = 63958dfaf0875c064bd0465a77388f29affea407d457cc278bdf26f20c1166f8
R2_BUCKET_NAME = scriptovideo
R2_PUBLIC_URL = https://pub-12f91b2f5d5d4412a751b7664f66fbb4.r2.dev
NEXT_PUBLIC_DEMO_VIDEOS_URL = https://pub-49364ecf52e344d3a722a3c5bca11271.r2.dev
```

### 方法2：使用 Vercel CLI（更快）

```bash
# 登录 Vercel
vercel login

# 链接项目
vercel link

# 批量添加环境变量
vercel env add NEXT_PUBLIC_CUSTOM_ANALYTICS_DOMAIN production
# 然后输入值：scripto.video

vercel env add NEXT_PUBLIC_CUSTOM_ANALYTICS_URL production
# 然后输入值：https://click.pageview.click/js/script.js
```

## ❓ 常见问题

### Q: 为什么文件名前面要有点？
A: 这是 Next.js 的约定，只有 `.env.*` 格式的文件才会被自动加载。

### Q: 本地开发需要配置什么？
A: 最少只需要上面的"最简配置"就能运行项目。

### Q: 线上部署需要配置什么？
A: 复制"生产环境必需配置"到 Vercel 环境变量即可。

### Q: 如果要用数据库功能怎么办？
A: 注册 Supabase 账号，创建项目，然后添加 Supabase 配置。

## 🎉 完成！

配置完成后，运行 `npm run dev` 或 `bun dev` 启动项目。 
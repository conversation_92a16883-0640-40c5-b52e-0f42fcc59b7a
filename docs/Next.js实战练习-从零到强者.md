# 🚀 Next.js实战练习 - 从零到强者

## 🎯 基于当前veo3.us项目的实战学习

### 📚 **练习1：理解当前项目结构 (第1天)**

#### 🔍 **任务1.1：分析根布局文件**
```typescript
// 当前项目的 src/app/layout.tsx 分析
import type { Metadata } from "next";
import { Geist, Geist_Mono } from "next/font/google";
import "./globals.css";
import ClientBody from "./ClientBody";
import SessionProvider from "@/components/providers/SessionProvider";

// 🔤 字体配置 - 这里使用了Google字体
const geistSans = Geist({
  variable: "--font-geist-sans",  // CSS变量名
  subsets: ["latin"],             // 字符集
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// 📄 页面元数据 - 影响SEO和浏览器标题
export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

// 🏗️ 根布局组件 - 所有页面的外层包装
export default function RootLayout({
  children,  // 这里会渲染各个页面的内容
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${geistSans.variable} ${geistMono.variable}`}>
      <body suppressHydrationWarning className="antialiased">
        {/* 🔐 会话提供者 - 管理用户登录状态 */}
        <SessionProvider>
          {/* 📱 客户端主体 - 处理客户端逻辑 */}
          <ClientBody>{children}</ClientBody>
        </SessionProvider>
      </body>
    </html>
  );
}
```

**🎯 你的任务：**
1. 打开 `src/app/layout.tsx` 文件
2. 理解每一行代码的作用
3. 尝试修改 `metadata` 中的 `title` 和 `description`
4. 保存文件，在浏览器中查看标题变化

#### 🔍 **任务1.2：分析首页文件**
```typescript
// 当前项目的 src/app/page.tsx 关键部分分析

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* 🧭 固定头部导航 */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          {/* 📍 左侧：Logo和导航 */}
          <div className="flex items-center space-x-8">
            <div className="text-xl md:text-2xl font-bold text-primary">
              Veo 3
            </div>
            <nav className="hidden md:flex items-center space-x-6">
              <Link href="/" className="text-primary">Home</Link>
              <Link href="/dashboard" className="text-foreground hover:text-primary transition-colors">Generate</Link>
              <Link href="/pricing" className="text-foreground hover:text-primary transition-colors">Pricing</Link>
            </nav>
          </div>
          
          {/* 📍 右侧：登录和注册按钮 */}
          <div className="flex items-center space-x-2 md:space-x-4">
            <Button variant="ghost" size="sm" className="hidden sm:inline-flex">Login</Button>
            <Button size="sm" className="bg-primary text-primary-foreground hover:bg-primary/90 text-xs md:text-sm px-3 md:px-4">
              Register
            </Button>
          </div>
        </div>
      </header>

      {/* 🎨 英雄区域 */}
      <section className="pt-24 pb-16 px-4">
        <div className="container mx-auto max-w-4xl text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 leading-tight">
            Veo 3: AI Video Generation with{" "}
            <span className="gradient-text">Realistic Sound</span>
          </h1>
          <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-2xl mx-auto px-4 md:px-0">
            Generate videos with perfectly synced audio, including sound effects, dialogue,
            and ambient noise. Bring your stories to life with Veo 3.
          </p>
          <Link href="/dashboard">
            <Button size="lg" className="bg-primary text-primary-foreground hover:bg-primary/90 px-6 md:px-8 py-3 md:py-4 text-base md:text-lg">
              Start Creating Now
            </Button>
          </Link>
        </div>
      </section>
      
      {/* 📺 其他组件 */}
      <TrendingVideos />
      <VideoShowcase />
      <KeyFeatures />
      <HowToSteps />
      <FAQ />
      <Footer />
    </div>
  )
}
```

**🎯 你的任务：**
1. 理解页面的整体结构：header + hero + sections
2. 注意 `className` 中的Tailwind CSS类名
3. 理解响应式设计：`sm:` `md:` `lg:` `xl:` 前缀
4. 尝试修改标题文字，观察变化

---

### 📚 **练习2：创建你的第一个页面 (第2天)**

#### 🎯 **任务2.1：创建关于页面**
```typescript
// 创建 src/app/about/page.tsx
export default function AboutPage() {
  return (
    <div className="min-h-screen pt-24 px-4">
      <div className="container mx-auto max-w-4xl">
        <h1 className="text-4xl font-bold text-center mb-8">
          关于我们
        </h1>
        <div className="prose prose-lg mx-auto">
          <p className="text-lg text-muted-foreground mb-6">
            欢迎来到Veo 3，最先进的AI视频生成平台。
          </p>
          <h2 className="text-2xl font-semibold mb-4">我们的使命</h2>
          <p className="mb-6">
            我们致力于让每个人都能轻松创建高质量的AI视频内容。
          </p>
          <h2 className="text-2xl font-semibold mb-4">技术特色</h2>
          <ul className="list-disc list-inside space-y-2 mb-6">
            <li>先进的AI视频生成技术</li>
            <li>实时音频同步</li>
            <li>高质量视频输出</li>
            <li>简单易用的界面</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
```

**🎯 操作步骤：**
1. 在 `src/app/` 目录下创建 `about` 文件夹
2. 在 `about` 文件夹中创建 `page.tsx` 文件
3. 复制上面的代码到文件中
4. 保存文件
5. 在浏览器中访问 `http://localhost:3000/about`

#### 🎯 **任务2.2：在导航中添加关于链接**
```typescript
// 修改 src/app/page.tsx 中的导航部分
<nav className="hidden md:flex items-center space-x-6">
  <Link href="/" className="text-primary">Home</Link>
  <Link href="/dashboard" className="text-foreground hover:text-primary transition-colors">Generate</Link>
  <Link href="/pricing" className="text-foreground hover:text-primary transition-colors">Pricing</Link>
  {/* 🆕 添加这一行 */}
  <Link href="/about" className="text-foreground hover:text-primary transition-colors">About</Link>
</nav>
```

---

### 📚 **练习3：创建你的第一个组件 (第3天)**

#### 🎯 **任务3.1：创建用户卡片组件**
```typescript
// 创建 src/components/UserCard.tsx
interface UserCardProps {
  name: string
  email: string
  avatar?: string
  role: string
  joinDate: string
}

export default function UserCard({ 
  name, 
  email, 
  avatar, 
  role, 
  joinDate 
}: UserCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-md border p-6 max-w-sm">
      {/* 🖼️ 头像区域 */}
      <div className="flex items-center mb-4">
        <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center mr-4">
          {avatar ? (
            <img 
              src={avatar} 
              alt={name}
              className="w-12 h-12 rounded-full object-cover"
            />
          ) : (
            <span className="text-gray-600 font-semibold">
              {name.charAt(0).toUpperCase()}
            </span>
          )}
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{name}</h3>
          <p className="text-sm text-gray-600">{role}</p>
        </div>
      </div>
      
      {/* 📧 详细信息 */}
      <div className="space-y-2">
        <div className="flex items-center text-sm text-gray-600">
          <span className="font-medium mr-2">邮箱:</span>
          <span>{email}</span>
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <span className="font-medium mr-2">加入时间:</span>
          <span>{joinDate}</span>
        </div>
      </div>
      
      {/* 🔘 操作按钮 */}
      <div className="mt-4 flex space-x-2">
        <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
          查看详情
        </button>
        <button className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors">
          发送消息
        </button>
      </div>
    </div>
  )
}
```

#### 🎯 **任务3.2：创建团队页面使用组件**
```typescript
// 创建 src/app/team/page.tsx
import UserCard from '@/components/UserCard'

export default function TeamPage() {
  const teamMembers = [
    {
      name: "张三",
      email: "<EMAIL>",
      role: "产品经理",
      joinDate: "2024-01-15"
    },
    {
      name: "李四",
      email: "<EMAIL>", 
      role: "前端开发",
      joinDate: "2024-02-01"
    },
    {
      name: "王五",
      email: "<EMAIL>",
      role: "AI工程师", 
      joinDate: "2024-01-20"
    }
  ]
  
  return (
    <div className="min-h-screen pt-24 px-4">
      <div className="container mx-auto max-w-6xl">
        <h1 className="text-4xl font-bold text-center mb-12">
          我们的团队
        </h1>
        
        {/* 🏢 团队成员网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {teamMembers.map((member, index) => (
            <UserCard
              key={index}
              name={member.name}
              email={member.email}
              role={member.role}
              joinDate={member.joinDate}
            />
          ))}
        </div>
      </div>
    </div>
  )
}
```

**🎯 操作步骤：**
1. 创建 `src/components/UserCard.tsx` 文件
2. 创建 `src/app/team/page.tsx` 文件
3. 复制代码并保存
4. 访问 `http://localhost:3000/team` 查看效果

---

### 📚 **练习4：创建你的第一个API (第4天)**

#### 🎯 **任务4.1：创建用户API**
```typescript
// 创建 src/app/api/users/route.ts
import { NextRequest } from 'next/server'

// 模拟用户数据
const users = [
  { id: 1, name: "张三", email: "<EMAIL>", role: "管理员" },
  { id: 2, name: "李四", email: "<EMAIL>", role: "用户" },
  { id: 3, name: "王五", email: "<EMAIL>", role: "用户" }
]

// GET请求 - 获取所有用户
export async function GET() {
  try {
    // 模拟数据库查询延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return Response.json({
      success: true,
      data: users,
      message: "获取用户列表成功"
    })
  } catch (error) {
    return Response.json(
      { success: false, error: "获取用户失败" },
      { status: 500 }
    )
  }
}

// POST请求 - 创建新用户
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, role } = body
    
    // 数据验证
    if (!name || !email) {
      return Response.json(
        { success: false, error: "姓名和邮箱是必需的" },
        { status: 400 }
      )
    }
    
    // 检查邮箱是否已存在
    const existingUser = users.find(user => user.email === email)
    if (existingUser) {
      return Response.json(
        { success: false, error: "邮箱已存在" },
        { status: 400 }
      )
    }
    
    // 创建新用户
    const newUser = {
      id: users.length + 1,
      name,
      email,
      role: role || "用户"
    }
    
    users.push(newUser)
    
    return Response.json({
      success: true,
      data: newUser,
      message: "用户创建成功"
    })
    
  } catch (error) {
    return Response.json(
      { success: false, error: "创建用户失败" },
      { status: 500 }
    )
  }
}
```

#### 🎯 **任务4.2：创建用户管理页面**
```typescript
// 创建 src/app/users/page.tsx
'use client'
import { useState, useEffect } from 'react'

interface User {
  id: number
  name: string
  email: string
  role: string
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  
  // 新用户表单状态
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    role: '用户'
  })
  const [submitting, setSubmitting] = useState(false)
  
  // 获取用户列表
  useEffect(() => {
    fetchUsers()
  }, [])
  
  const fetchUsers = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/users')
      const result = await response.json()
      
      if (result.success) {
        setUsers(result.data)
      } else {
        setError(result.error)
      }
    } catch (err) {
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }
  
  // 创建新用户
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)
    
    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newUser)
      })
      
      const result = await response.json()
      
      if (result.success) {
        setUsers([...users, result.data])
        setNewUser({ name: '', email: '', role: '用户' })
        alert('用户创建成功！')
      } else {
        alert('创建失败：' + result.error)
      }
    } catch (err) {
      alert('网络错误')
    } finally {
      setSubmitting(false)
    }
  }
  
  if (loading) {
    return (
      <div className="min-h-screen pt-24 px-4 flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="min-h-screen pt-24 px-4 flex items-center justify-center">
        <div className="text-red-600">错误：{error}</div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen pt-24 px-4">
      <div className="container mx-auto max-w-6xl">
        <h1 className="text-4xl font-bold text-center mb-12">
          用户管理
        </h1>
        
        {/* 📝 添加用户表单 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4">添加新用户</h2>
          <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <input
              type="text"
              placeholder="姓名"
              value={newUser.name}
              onChange={(e) => setNewUser({...newUser, name: e.target.value})}
              className="border border-gray-300 rounded-md px-3 py-2"
              required
            />
            <input
              type="email"
              placeholder="邮箱"
              value={newUser.email}
              onChange={(e) => setNewUser({...newUser, email: e.target.value})}
              className="border border-gray-300 rounded-md px-3 py-2"
              required
            />
            <select
              value={newUser.role}
              onChange={(e) => setNewUser({...newUser, role: e.target.value})}
              className="border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="用户">用户</option>
              <option value="管理员">管理员</option>
            </select>
            <button
              type="submit"
              disabled={submitting}
              className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {submitting ? '创建中...' : '创建用户'}
            </button>
          </form>
        </div>
        
        {/* 📊 用户列表 */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  姓名
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  邮箱
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  角色
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {user.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {user.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {user.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.role === '管理员' 
                        ? 'bg-red-100 text-red-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {user.role}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
```

**🎯 操作步骤：**
1. 创建 `src/app/api/users/route.ts` 文件
2. 创建 `src/app/users/page.tsx` 文件
3. 复制代码并保存
4. 访问 `http://localhost:3000/users` 测试功能

---

### 📚 **练习5：状态管理实战 (第5天)**

#### 🎯 **任务5.1：创建计数器状态管理**
```typescript
// 创建 src/lib/store/counterStore.ts
import { create } from 'zustand'

interface CounterState {
  count: number
  increment: () => void
  decrement: () => void
  reset: () => void
  setCount: (count: number) => void
}

export const useCounterStore = create<CounterState>((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
  reset: () => set({ count: 0 }),
  setCount: (count) => set({ count }),
}))
```

#### 🎯 **任务5.2：创建计数器组件**
```typescript
// 创建 src/components/Counter.tsx
'use client'
import { useCounterStore } from '@/lib/store/counterStore'

export default function Counter() {
  const { count, increment, decrement, reset, setCount } = useCounterStore()
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6 max-w-sm mx-auto">
      <h2 className="text-2xl font-bold text-center mb-4">计数器</h2>
      
      {/* 🔢 显示当前计数 */}
      <div className="text-6xl font-bold text-center mb-6 text-blue-600">
        {count}
      </div>
      
      {/* 🔘 操作按钮 */}
      <div className="grid grid-cols-2 gap-3 mb-4">
        <button
          onClick={increment}
          className="bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 transition-colors"
        >
          +1
        </button>
        <button
          onClick={decrement}
          className="bg-red-500 text-white py-2 px-4 rounded-md hover:bg-red-600 transition-colors"
        >
          -1
        </button>
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <button
          onClick={reset}
          className="bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors"
        >
          重置
        </button>
        <button
          onClick={() => setCount(10)}
          className="bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition-colors"
        >
          设为10
        </button>
      </div>
      
      {/* 📊 计数状态 */}
      <div className="mt-4 text-center text-sm text-gray-600">
        {count === 0 && "计数为零"}
        {count > 0 && `正数：${count}`}
        {count < 0 && `负数：${Math.abs(count)}`}
      </div>
    </div>
  )
}
```

#### 🎯 **任务5.3：创建演示页面**
```typescript
// 创建 src/app/demo/page.tsx
import Counter from '@/components/Counter'

export default function DemoPage() {
  return (
    <div className="min-h-screen pt-24 px-4">
      <div className="container mx-auto max-w-4xl">
        <h1 className="text-4xl font-bold text-center mb-12">
          功能演示
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* 🔢 计数器演示 */}
          <div>
            <h2 className="text-2xl font-semibold mb-4 text-center">
              状态管理演示
            </h2>
            <Counter />
          </div>
          
          {/* 📝 说明文档 */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">功能说明</h3>
            <ul className="space-y-2 text-gray-700">
              <li>• 使用Zustand进行状态管理</li>
              <li>• 支持增加、减少、重置操作</li>
              <li>• 实时状态更新</li>
              <li>• 响应式设计</li>
            </ul>
            
            <h3 className="text-xl font-semibold mb-4 mt-6">技术栈</h3>
            <div className="flex flex-wrap gap-2">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">Next.js</span>
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">TypeScript</span>
              <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">Zustand</span>
              <span className="bg-cyan-100 text-cyan-800 px-2 py-1 rounded text-sm">Tailwind CSS</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
```

---

## 🎯 每日学习检查清单

### ✅ **第1天检查清单**
- [ ] 理解了layout.tsx的作用
- [ ] 理解了page.tsx的结构
- [ ] 成功修改了页面标题
- [ ] 理解了Tailwind CSS基础类名

### ✅ **第2天检查清单**
- [ ] 成功创建了about页面
- [ ] 理解了文件路径=网址的关系
- [ ] 在导航中添加了新链接
- [ ] 页面可以正常访问

### ✅ **第3天检查清单**
- [ ] 创建了第一个组件UserCard
- [ ] 理解了Props的概念和使用
- [ ] 创建了team页面并使用了组件
- [ ] 理解了组件复用的好处

### ✅ **第4天检查清单**
- [ ] 创建了第一个API接口
- [ ] 理解了GET和POST请求
- [ ] 创建了用户管理页面
- [ ] 成功实现了前后端数据交互

### ✅ **第5天检查清单**
- [ ] 理解了Zustand状态管理
- [ ] 创建了计数器组件
- [ ] 实现了状态的增删改查
- [ ] 创建了完整的演示页面

## 🚀 下一步学习建议

1. **深入学习认证系统** - 研究NextAuth的使用
2. **学习数据库操作** - 掌握Prisma ORM
3. **掌握支付集成** - 理解Stripe支付流程
4. **性能优化** - 学习Next.js的优化技巧
5. **部署上线** - 学习Vercel部署

记住：**编程是实践的艺术，多写代码，多解决问题，你就会越来越强！** 💪 
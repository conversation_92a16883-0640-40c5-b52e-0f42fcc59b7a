# 🏗️ 当前项目实际文件结构详解

## 📁 项目根目录结构

```
veo3.us/                           ← 项目根目录
├── 📁 src/                        ← 源代码目录（最重要！）
├── 📁 docs/                       ← 项目文档
├── 📁 prisma/                     ← 数据库配置和迁移
├── 📁 scripts/                    ← 脚本文件
├── 📁 template/                   ← 模板文件
├── 📁 node_modules/               ← 依赖包（自动生成，不要动）
├── 📁 .next/                      ← Next.js编译文件（自动生成，不要动）
├── 📁 .git/                       ← Git版本控制（不要动）
│
├── 📄 package.json                ← 项目配置和依赖
├── 📄 package-lock.json           ← 依赖锁定文件
├── 📄 bun.lock                    ← Bun包管理器锁定文件
├── 📄 next.config.js              ← Next.js配置
├── 📄 tsconfig.json               ← TypeScript配置
├── 📄 tailwind.config.ts          ← Tailwind CSS配置
├── 📄 components.json             ← Shadcn UI组件配置
├── 📄 eslint.config.mjs           ← 代码检查配置
├── 📄 biome.json                  ← 代码格式化配置
├── 📄 postcss.config.mjs          ← CSS处理配置
├── 📄 netlify.toml                ← Netlify部署配置
├── 📄 .gitignore                  ← Git忽略文件配置
├── 📄 env.example                 ← 环境变量示例
├── 📄 README.md                   ← 项目说明文档
└── 📄 TODO.md                     ← 待办事项
```

---

## 🎯 核心目录：src/ 详解

```
src/                               ← 所有源代码都在这里
├── 📁 app/                        ← Next.js App Router（页面和API）
├── 📁 components/                 ← 可复用UI组件
├── 📁 lib/                        ← 工具函数、配置、服务
├── 📁 types/                      ← TypeScript类型定义
└── 📁 generated/                  ← 自动生成的文件
```

---

## 📱 app/ 目录详解（页面和API）

### 🏠 **页面文件**
```
src/app/
├── 📄 layout.tsx                  ← 根布局（导航栏、页脚等）
├── 📄 page.tsx                    ← 首页 (/)
├── 📄 globals.css                 ← 全局样式
├── 📄 not-found.tsx               ← 404错误页面
├── 📄 ClientBody.tsx              ← 客户端主体组件
│
├── 📁 auth/                       ← 用户认证页面
│   ├── signin/page.tsx            ← 登录页面 (/auth/signin)
│   ├── signup/page.tsx            ← 注册页面 (/auth/signup)
│   └── callback/page.tsx          ← OAuth回调页面
│
├── 📁 dashboard/                  ← 用户仪表板
│   ├── layout.tsx                 ← 仪表板布局
│   ├── page.tsx                   ← 仪表板首页 (/dashboard)
│   ├── profile/page.tsx           ← 用户资料 (/dashboard/profile)
│   ├── videos/page.tsx            ← 视频管理 (/dashboard/videos)
│   └── billing/page.tsx           ← 账单管理 (/dashboard/billing)
│
├── 📁 pricing/                    ← 定价页面
│   └── page.tsx                   ← 定价表 (/pricing)
│
└── 📁 admin/                      ← 管理员页面
    ├── layout.tsx                 ← 管理员布局
    ├── page.tsx                   ← 管理员首页 (/admin)
    ├── users/page.tsx             ← 用户管理 (/admin/users)
    └── analytics/page.tsx         ← 数据分析 (/admin/analytics)
```

### 🔌 **API接口**
```
src/app/api/                       ← 后端API接口
├── 📁 auth/                       ← 认证相关API
│   └── [...nextauth]/route.ts     ← NextAuth配置
│
├── 📁 payment/                    ← 支付相关API
│   ├── create-session/route.ts    ← 创建支付会话
│   ├── verify-payment/route.ts    ← 验证支付状态
│   └── cancel-subscription/route.ts ← 取消订阅
│
├── 📁 webhooks/                   ← 第三方服务回调
│   ├── stripe/route.ts            ← Stripe支付回调
│   └── creem/route.ts             ← Creem支付回调
│
├── 📁 user/                       ← 用户相关API
│   ├── profile/route.ts           ← 用户资料API
│   ├── credits/route.ts           ← 积分管理API
│   └── videos/route.ts            ← 视频管理API
│
├── 📁 video/                      ← 视频生成API
│   ├── generate/route.ts          ← 生成视频
│   ├── status/route.ts            ← 查询生成状态
│   └── download/route.ts          ← 下载视频
│
└── 📁 admin/                      ← 管理员API
    ├── users/route.ts             ← 用户管理API
    ├── analytics/route.ts         ← 数据分析API
    └── maintenance/route.ts       ← 系统维护API
```

---

## 🧩 components/ 目录详解（UI组件）

```
src/components/
├── 📁 ui/                         ← 基础UI组件（Shadcn UI）
│   ├── button.tsx                 ← 按钮组件
│   ├── input.tsx                  ← 输入框组件
│   ├── card.tsx                   ← 卡片组件
│   ├── dialog.tsx                 ← 对话框组件
│   ├── toast.tsx                  ← 提示组件
│   └── ...更多基础组件
│
├── 📁 providers/                  ← 全局状态提供者
│   ├── auth-provider.tsx          ← 认证状态提供者
│   ├── theme-provider.tsx         ← 主题状态提供者
│   └── toast-provider.tsx         ← 提示状态提供者
│
├── 📁 animations/                 ← 动画组件
│   ├── fade-in.tsx                ← 淡入动画
│   ├── slide-up.tsx               ← 滑入动画
│   └── loading-spinner.tsx        ← 加载动画
│
├── 📄 VideoShowcase.tsx           ← 视频展示组件
├── 📄 TrendingVideos.tsx          ← 热门视频组件
├── 📄 Footer.tsx                  ← 页脚组件
├── 📄 HowToSteps.tsx              ← 使用步骤组件
├── 📄 KeyFeatures.tsx             ← 核心功能组件
└── 📄 FAQ.tsx                     ← 常见问题组件
```

---

## ⚙️ lib/ 目录详解（核心逻辑）

```
src/lib/
├── 📁 config/                     ← 配置文件
│   ├── payment.ts                 ← 支付配置（重要！你要修改的）
│   ├── database.ts                ← 数据库配置
│   ├── auth.ts                    ← 认证配置
│   └── app.ts                     ← 应用配置
│
├── 📁 services/                   ← 业务服务
│   ├── payment-database.ts        ← 支付数据库操作
│   ├── user-service.ts            ← 用户服务
│   ├── video-service.ts           ← 视频服务
│   ├── email-service.ts           ← 邮件服务
│   └── analytics-service.ts       ← 分析服务
│
├── 📁 payment/                    ← 支付系统
│   ├── stripe.ts                  ← Stripe集成
│   ├── creem.ts                   ← Creem集成
│   ├── webhook-handlers.ts        ← Webhook处理
│   └── pricing-calculator.ts      ← 价格计算
│
├── 📁 utils/                      ← 工具函数
│   ├── response.ts                ← API响应工具
│   ├── validation.ts              ← 数据验证
│   ├── format.ts                  ← 格式化工具
│   ├── encryption.ts              ← 加密工具
│   └── date.ts                    ← 日期工具
│
├── 📁 types/                      ← 类型定义
│   ├── payment.ts                 ← 支付相关类型
│   ├── user.ts                    ← 用户相关类型
│   ├── video.ts                   ← 视频相关类型
│   └── api.ts                     ← API相关类型
│
├── 📁 tasks/                      ← 后台任务
│   ├── cleanup.ts                 ← 清理任务
│   ├── notifications.ts           ← 通知任务
│   └── analytics.ts               ← 分析任务
│
├── 📄 auth.ts                     ← NextAuth主配置
├── 📄 prisma.ts                   ← Prisma数据库客户端
├── 📄 payment.ts                  ← 主支付逻辑
└── 📄 utils.ts                    ← 通用工具函数
```

---

## 🎯 重要文件详细说明

### 📄 **src/app/layout.tsx - 根布局**
```typescript
// 这个文件定义了整个网站的基本HTML结构
// 每个页面都会被这个布局包裹
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body>
        <AuthProvider>        {/* 认证状态管理 */}
          <ThemeProvider>     {/* 主题管理 */}
            <Navbar />        {/* 导航栏 */}
            <main>
              {children}      {/* 具体页面内容 */}
            </main>
            <Footer />        {/* 页脚 */}
            <Toaster />       {/* 全局提示 */}
          </ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
```

### 📄 **src/app/page.tsx - 首页**
```typescript
// 这是用户访问网站时看到的第一个页面
export default function HomePage() {
  return (
    <>
      <HeroSection />         {/* 英雄区域：标题、描述、CTA按钮 */}
      <VideoShowcase />       {/* 视频展示：演示AI生成效果 */}
      <KeyFeatures />         {/* 核心功能：介绍主要特性 */}
      <HowToSteps />          {/* 使用步骤：如何使用产品 */}
      <TrendingVideos />      {/* 热门视频：用户生成的优秀作品 */}
      <FAQ />                 {/* 常见问题：解答用户疑问 */}
    </>
  )
}
```

### 📄 **src/lib/config/payment.ts - 支付配置（重要！）**
```typescript
// 这是你需要修改的支付配置文件
export const PAYMENT_CONFIG = {
  // 支付系统开关
  STRIPE_ENABLED: true,           // 是否启用Stripe
  CREEM_ENABLED: true,            // 是否启用Creem
  
  // 默认支付提供商
  DEFAULT_PROVIDER: "creem",      // "stripe" | "creem"
  
  // 维护模式
  MAINTENANCE_MODE: false,        // 紧急情况下暂停所有支付
  
  // 其他配置...
}
```

### 📄 **src/lib/auth.ts - 认证配置**
```typescript
// NextAuth配置，处理用户登录
export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({              // Google登录
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    GitHubProvider({              // GitHub登录
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    // 登录成功后的回调处理
    jwt: async ({ token, user, account }) => {
      // 处理JWT token
    },
    session: async ({ session, token }) => {
      // 处理用户会话
    },
  },
}
```

---

## 🔄 数据流向理解

### 📊 **用户操作 → 系统响应流程**

#### 1️⃣ **用户访问首页**
```
用户输入网址 
→ Next.js路由系统 
→ src/app/page.tsx 
→ 渲染首页组件 
→ 显示给用户
```

#### 2️⃣ **用户点击登录**
```
用户点击登录按钮 
→ 跳转到 /auth/signin 
→ src/app/auth/signin/page.tsx 
→ 显示登录表单 
→ 用户选择登录方式 
→ NextAuth处理认证 
→ 重定向到仪表板
```

#### 3️⃣ **用户购买积分**
```
用户点击购买 
→ 跳转到 /pricing 
→ src/app/pricing/page.tsx 
→ 用户选择套餐 
→ 调用 /api/payment/create-session 
→ 根据配置选择支付提供商 
→ 创建支付会话 
→ 跳转到支付页面
```

#### 4️⃣ **支付完成处理**
```
用户完成支付 
→ 支付提供商发送Webhook 
→ /api/webhooks/stripe 或 /api/webhooks/creem 
→ 验证支付签名 
→ 更新数据库订单状态 
→ 给用户添加积分 
→ 发送确认邮件
```

#### 5️⃣ **用户生成视频**
```
用户输入视频描述 
→ 调用 /api/video/generate 
→ 检查用户积分 
→ 调用AI服务生成视频 
→ 扣除用户积分 
→ 保存视频记录 
→ 返回视频结果
```

---

## 🎮 实际操作练习

### 🚀 **练习1：找到并理解重要文件**

#### 任务：定位关键配置文件
1. **找到支付配置文件**
   ```bash
   文件位置：src/lib/config/payment.ts
   作用：控制双支付系统的开关和配置
   ```

2. **找到首页文件**
   ```bash
   文件位置：src/app/page.tsx
   作用：网站首页的内容和布局
   ```

3. **找到认证配置文件**
   ```bash
   文件位置：src/lib/auth.ts
   作用：配置用户登录方式和认证逻辑
   ```

### 🚀 **练习2：理解文件路径和网址的关系**

#### 文件路径 → 网址映射
```
src/app/page.tsx                    → yoursite.com/
src/app/pricing/page.tsx            → yoursite.com/pricing
src/app/auth/signin/page.tsx        → yoursite.com/auth/signin
src/app/dashboard/page.tsx          → yoursite.com/dashboard
src/app/dashboard/profile/page.tsx  → yoursite.com/dashboard/profile
src/app/admin/page.tsx              → yoursite.com/admin
```

### 🚀 **练习3：理解API路径和功能**

#### API路径 → 功能映射
```
src/app/api/auth/[...nextauth]/route.ts  → 处理用户登录认证
src/app/api/payment/create-session/route.ts → 创建支付会话
src/app/api/webhooks/stripe/route.ts     → 处理Stripe支付回调
src/app/api/webhooks/creem/route.ts      → 处理Creem支付回调
src/app/api/user/credits/route.ts        → 管理用户积分
src/app/api/video/generate/route.ts      → 生成AI视频
```

---

## 🎯 小白必须理解的核心概念

### 📚 **1. 文件即路由**
- 在`src/app/`目录下创建`page.tsx`文件 = 创建一个网页
- 文件夹名称 = 网址路径
- 这是Next.js的"约定优于配置"理念

### 📚 **2. 组件化开发**
- `src/components/`下的文件是可复用的UI组件
- 写一次，在多个页面中使用
- 修改组件，所有使用的地方都会更新

### 📚 **3. 配置文件的重要性**
- `src/lib/config/`下的文件控制系统行为
- 修改配置文件可以改变系统功能
- 这是你控制双支付系统的关键

### 📚 **4. API路由处理后端逻辑**
- `src/app/api/`下的文件处理后端业务
- 前端页面通过API与后端通信
- 这是全栈开发的核心

### 📚 **5. 数据库操作**
- `src/lib/services/`下的文件处理数据库操作
- 通过Prisma ORM与PostgreSQL数据库交互
- 所有数据的增删改查都在这里

---

## 🎯 总结：你现在应该掌握的

### ✅ **文件结构理解**
1. **根目录**：项目配置和依赖
2. **src/app/**：页面和API接口
3. **src/components/**：可复用UI组件
4. **src/lib/**：核心业务逻辑和配置

### ✅ **核心概念掌握**
1. **文件路径 = 网址**：理解Next.js路由系统
2. **组件化开发**：理解代码复用
3. **API路由**：理解前后端分离
4. **配置驱动**：理解如何控制系统行为

### ✅ **项目特色功能**
1. **AI视频生成**：核心业务功能
2. **双支付系统**：Stripe + Creem智能切换
3. **用户认证**：NextAuth + OAuth登录
4. **积分系统**：购买积分生成视频

### 🎮 **下一步学习建议**
1. **查看实际文件**：打开上面提到的重要文件，看看实际代码
2. **修改配置练习**：尝试修改`src/lib/config/payment.ts`
3. **创建简单页面**：在`src/app/`下创建新的页面
4. **理解组件使用**：查看`src/components/`下的组件如何被使用

现在你应该对整个项目有了清晰的理解！有任何具体问题都可以继续问我。 
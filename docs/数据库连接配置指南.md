# 🗄️ 数据库连接配置指南

## 🎯 当前状态：测试账户模式

**你现在可以直接使用，无需任何数据库配置！**

### 测试账户信息
- **邮箱**: `<EMAIL>`
- **密码**: `password`
- **访问**: `http://localhost:3000/test-auth`

这是硬编码在代码中的测试账户，不需要数据库。

## 🚀 升级到真实数据库（可选）

### 方案1：使用 Supabase（推荐）

#### 步骤1：创建 Supabase 项目
1. 访问 [supabase.com](https://supabase.com)
2. 创建新项目
3. 获取项目 URL 和 API Key

#### 步骤2：配置环境变量
```env
# .env.local
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
```

#### 步骤3：修改认证逻辑
```typescript
// src/lib/auth.ts 中的 authorize 函数
async authorize(credentials) {
  if (!credentials?.email || !credentials?.password) {
    return null
  }

  // 🔄 替换测试账户为 Supabase 认证
  const { createClient } = require('@supabase/supabase-js')
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  const { data, error } = await supabase.auth.signInWithPassword({
    email: credentials.email,
    password: credentials.password,
  })

  if (data.user && !error) {
    return {
      id: data.user.id,
      email: data.user.email,
      name: data.user.user_metadata?.name || data.user.email,
    }
  }

  return null
}
```

### 方案2：使用自建数据库

#### 步骤1：安装数据库依赖
```bash
npm install prisma @prisma/client bcryptjs
npm install -D @types/bcryptjs
```

#### 步骤2：配置 Prisma
```prisma
// prisma/schema.prisma
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String?
  createdAt DateTime @default(now())
}
```

#### 步骤3：修改认证逻辑
```typescript
// src/lib/auth.ts 中的 authorize 函数
async authorize(credentials) {
  if (!credentials?.email || !credentials?.password) {
    return null
  }

  // 🔄 替换测试账户为数据库查询
  const { PrismaClient } = require('@prisma/client')
  const bcrypt = require('bcryptjs')
  const prisma = new PrismaClient()

  const user = await prisma.user.findUnique({
    where: { email: credentials.email }
  })

  if (user && await bcrypt.compare(credentials.password, user.password)) {
    return {
      id: user.id,
      email: user.email,
      name: user.name,
    }
  }

  return null
}
```

## 🎯 推荐方案对比

| 方案 | 复杂度 | 成本 | 维护 | 推荐度 |
|------|--------|------|------|--------|
| **测试账户** | ⭐ | 免费 | 无 | 🎯 开发测试 |
| **Supabase** | ⭐⭐ | 免费额度 | 低 | 🎯 生产推荐 |
| **自建数据库** | ⭐⭐⭐⭐ | 服务器费用 | 高 | 🎯 企业级 |

## 🚀 我的建议

### 阶段1：学习和开发（当前）
**使用测试账户** - 无需任何配置，专注学习 NextAuth

### 阶段2：原型和演示
**升级到 Supabase** - 5分钟配置，支持真实用户注册

### 阶段3：生产部署
**根据需求选择** - Supabase（简单）或自建（控制）

## 🔧 需要我帮你升级吗？

如果你想升级到真实数据库，告诉我你选择哪个方案，我来帮你修改代码！

**当前你可以直接使用测试账户体验所有功能。** 🎉 
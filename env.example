# =============================================================================
# 🎨 FLUX KONTEXT AI - 环境变量配置模板
# =============================================================================
# 复制此文件为 .env.local 并填入真实值
# 注意：生产环境请使用 HTTPS 和安全的密钥

# =============================================================================
# 🚀 核心AI服务配置 (必需)
# =============================================================================

# FAL AI API密钥 - 用于图像生成
# 获取地址: https://fal.ai/dashboard
FAL_KEY="your_fal_api_key_here"

# =============================================================================
# 🗄️ 数据库配置 (必需)
# =============================================================================

# Supabase 数据库配置
# 获取地址: https://supabase.com/dashboard
NEXT_PUBLIC_SUPABASE_URL="https://your-project-id.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your_supabase_anon_key"
SUPABASE_SERVICE_ROLE_KEY="your_supabase_service_role_key"

# 数据库连接字符串 (Supabase PostgreSQL)
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres"

# =============================================================================
# 🔐 身份认证配置 (必需)
# =============================================================================

# NextAuth.js 配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your_nextauth_secret_here_minimum_32_characters"

# Google OAuth 配置 (必需)
# 获取地址: https://console.developers.google.com/
GOOGLE_ID="your_google_client_id"
GOOGLE_SECRET="your_google_client_secret"

# GitHub OAuth 配置 (可选)
NEXT_PUBLIC_AUTH_GITHUB_ENABLED="false"
AUTH_GITHUB_ID="your_github_client_id"
AUTH_GITHUB_SECRET="your_github_client_secret"

# Google OAuth 开关
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED="true"

# 凭据登录开关 (可选)
NEXT_PUBLIC_AUTH_CREDENTIALS_ENABLED="false"

# =============================================================================
# ☁️ 文件存储配置 (重要)
# =============================================================================

# Cloudflare R2 存储配置
# 获取地址: https://dash.cloudflare.com/
NEXT_PUBLIC_ENABLE_R2="true"
R2_ACCOUNT_ID="your_r2_account_id"
R2_ACCESS_KEY_ID="your_r2_access_key_id"
R2_SECRET_ACCESS_KEY="your_r2_secret_access_key"
R2_BUCKET_NAME="your_bucket_name"
R2_PUBLIC_URL="https://your-bucket-url.r2.dev"
R2_CUSTOM_DOMAIN=""

# 演示图像存储配置 (公共只读)
NEXT_PUBLIC_DEMO_IMAGES_URL="https://pub-49364ecf52e344d3a722a3c5bca11271.r2.dev"
NEXT_PUBLIC_DEMO_VIDEOS_URL="https://pub-49364ecf52e344d3a722a3c5bca11271.r2.dev"

# =============================================================================
# 💳 支付系统配置 (可选)
# =============================================================================

# Stripe 支付配置
# 获取地址: https://dashboard.stripe.com/
NEXT_PUBLIC_ENABLE_STRIPE="true"
STRIPE_PUBLIC_KEY="pk_test_your_stripe_publishable_key"
STRIPE_PRIVATE_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_stripe_webhook_secret"

# Creem 支付配置
# 获取地址: https://creem.io/
NEXT_PUBLIC_ENABLE_CREEM="false"
CREEM_API_KEY="your_creem_api_key"
CREEM_API_URL="https://api.creem.io/v1"
CREEM_WEBHOOK_SECRET="your_creem_webhook_secret"

# 支付系统控制
NEXT_PUBLIC_DEFAULT_PAYMENT_PROVIDER="stripe"
NEXT_PUBLIC_PAY_CANCEL_URL="https://fluxkontext.space/pricing"

# =============================================================================
# 🛡️ 安全防护配置 (可选)
# =============================================================================

# Cloudflare Turnstile 人机验证
# 获取地址: https://dash.cloudflare.com/
NEXT_PUBLIC_ENABLE_TURNSTILE="false"
NEXT_PUBLIC_TURNSTILE_SITE_KEY="your_turnstile_site_key"
TURNSTILE_SECRET_KEY="your_turnstile_secret_key"

# AI内容安全防护 (可选 - 建议初期关闭)
NEXT_PUBLIC_ENABLE_CONTENT_SAFETY="false"

# OpenAI API Key (仅在启用内容安全时需要)
# 获取地址: https://platform.openai.com/api-keys
OPENAI_API_KEY="your_openai_api_key_here"

# Google Cloud Vision API (可选)
GOOGLE_CLOUD_VISION_API_KEY="your_google_cloud_vision_api_key"

# API4AI 内容检测 (可选)
API4AI_API_KEY="your_api4ai_api_key"

# Azure Content Safety (可选)
AZURE_CONTENT_SAFETY_KEY="your_azure_content_safety_key"
AZURE_CONTENT_SAFETY_ENDPOINT="your_azure_content_safety_endpoint"

# =============================================================================
# 🌐 站点配置
# =============================================================================

# 站点基本信息
NEXT_PUBLIC_SITE_URL="https://fluxkontext.space"
NEXT_PUBLIC_WEB_URL="https://fluxkontext.space"
NEXT_PUBLIC_PROJECT_NAME="Flux Kontext"

# SEO配置
GOOGLE_SITE_VERIFICATION="your_google_site_verification_code"

# =============================================================================
# 🎬 AI视频生成配置 (可选)
# =============================================================================

# Segmind API (视频生成)
SEGMIND_API_KEY="your_segmind_api_key"

# Runway API (视频生成)
RUNWAY_API_KEY="your_runway_api_key"

# =============================================================================
# 📊 分析和监控配置 (可选)
# =============================================================================

# Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"

# Plausible Analytics
NEXT_PUBLIC_PLAUSIBLE_DOMAIN="fluxkontext.space"

# OpenPanel Analytics
NEXT_PUBLIC_OPENPANEL_CLIENT_ID=""

# Microsoft Clarity
NEXT_PUBLIC_CLARITY_PROJECT_ID=""

# 自定义分析服务
NEXT_PUBLIC_CUSTOM_ANALYTICS_DOMAIN="fluxkontext.space"
NEXT_PUBLIC_CUSTOM_ANALYTICS_URL="https://click.pageview.click/js/script.js"

# =============================================================================
# 📧 邮件服务配置 (可选)
# =============================================================================

# SMTP 邮件配置
SMTP_HOST=""
SMTP_PORT=""
SMTP_USER=""
SMTP_PASS=""

# =============================================================================
# 🔧 开发环境配置
# =============================================================================

# 环境设置
NODE_ENV="development"

# 主题配置
NEXT_PUBLIC_DEFAULT_THEME="dark"

# 管理员邮箱
ADMIN_EMAILS="<EMAIL>,<EMAIL>"

# 地理位置检测
IPAPI_KEY=""

# =============================================================================
# 🚀 Vercel 部署配置 (自动设置)
# =============================================================================

# Vercel 环境变量 (部署时自动设置)
# VERCEL="1"
# VERCEL_URL="your-app.vercel.app"

# =============================================================================
# 📝 配置说明
# =============================================================================

# 必需配置 (项目无法运行):
# - FAL_KEY
# - NEXT_PUBLIC_SUPABASE_URL
# - NEXT_PUBLIC_SUPABASE_ANON_KEY
# - SUPABASE_SERVICE_ROLE_KEY
# - NEXTAUTH_URL
# - NEXTAUTH_SECRET
# - GOOGLE_ID
# - GOOGLE_SECRET

# 重要配置 (影响核心功能):
# - R2存储配置 (图像存储)
# - 支付配置 (收费功能)

# 可选配置 (增强功能):
# - Turnstile (防盗刷)
# - 内容安全 (AI审核)
# - 分析服务 (数据统计)
# - 邮件服务 (通知功能)

# 安全提示:
# 1. 生产环境请使用强密码和HTTPS
# 2. 定期轮换API密钥
# 3. 不要在代码中硬编码敏感信息
# 4. 使用环境变量管理敏感配置

{"name": "nextjs-shadcn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3000", "dev:turbo": "next dev -H 0.0.0.0 --turbopack --port 3000", "dev:stable": "next dev --port 3000", "dev:clean": "powershell -Command \"3000..3010 | ForEach-Object { Get-NetTCPConnection -LocalPort $_ -ErrorAction SilentlyContinue | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force } }\" && next dev --port 3000", "kill:ports": "powershell -Command \"3000..3010 | ForEach-Object { Get-NetTCPConnection -LocalPort $_ -ErrorAction SilentlyContinue | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force } }\"", "kill:3000": "powershell -Command \"Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force }\"", "build": "next build", "start": "next start", "lint": "next lint && tsc --noEmit", "format": "npx @biomejs/biome format --write .", "setup": "node scripts/quick-setup.js", "check": "node scripts/check-config.js", "check:supabase": "node scripts/check-supabase.js", "perf": "node scripts/performance-check.js", "analyze": "npm run build && npx @next/bundle-analyzer", "setup:env": "cp env.example .env.local && node scripts/check-config.js", "seo:check": "node scripts/check-seo.js", "seo:audit": "npm run seo:check && npm run lighthouse", "lighthouse": "npx lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html", "test:api": "node scripts/test-api.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/lib-storage": "^3.817.0", "@fal-ai/client": "^1.5.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.47.10", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^9.0.8", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "dotenv": "^16.5.0", "framer-motion": "^12.14.0", "google-auth-library": "^9.15.1", "lucide-react": "^0.475.0", "next": "^15.3.2", "next-auth": "^4.24.11", "react": "^18.3.1", "react-dom": "^18.3.1", "stripe": "^17.5.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.4", "zustand": "^5.0.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3.3.1", "@types/node": "^20.17.50", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "eslint": "^9.27.0", "eslint-config-next": "15.1.7", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}